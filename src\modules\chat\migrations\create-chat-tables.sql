-- Migration: Create Chat Tables
-- Description: <PERSON><PERSON><PERSON> các bảng cho hệ thống chat tích hợp Facebook Messenger
-- Author: AI Assistant
-- Date: 2024-12-19

-- T<PERSON>o bảng chat_conversations
DROP TABLE IF EXISTS chat_conversations CASCADE;
CREATE TABLE chat_conversations (
    id SERIAL PRIMARY KEY,
    facebook_user_id VARCHAR(100) NOT NULL,
    page_id VARCHAR(100) NOT NULL,
    user_name VARCHAR(255),
    user_avatar VARCHAR(500),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    assigned_agent_id INTEGER,
    conversation_type VARCHAR(50) NOT NULL DEFAULT 'auto',
    language VARCHAR(10) NOT NULL DEFAULT 'vi',
    metadata JSONB,
    last_message_at BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT chat_conversations_facebook_user_page_unique UNIQUE (facebook_user_id, page_id, tenant_id)
);

-- T<PERSON>o index cho chat_conversations
CREATE INDEX idx_chat_conversations_facebook_user_page ON chat_conversations(facebook_user_id, page_id);
CREATE INDEX idx_chat_conversations_tenant_status ON chat_conversations(tenant_id, status);
CREATE INDEX idx_chat_conversations_assigned_agent ON chat_conversations(assigned_agent_id);
CREATE INDEX idx_chat_conversations_last_message ON chat_conversations(last_message_at DESC);

-- Tạo bảng chat_messages
DROP TABLE IF EXISTS chat_messages CASCADE;
CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    facebook_message_id VARCHAR(100),
    message_type VARCHAR(50) NOT NULL,
    content TEXT,
    attachments JSONB,
    direction VARCHAR(20) NOT NULL,
    sender_type VARCHAR(20) NOT NULL,
    sender_agent_id INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'sent',
    is_ai_generated BOOLEAN NOT NULL DEFAULT FALSE,
    ai_context JSONB,
    ai_confidence DECIMAL(3,2),
    detected_intent VARCHAR(100),
    extracted_entities JSONB,
    metadata JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT fk_chat_messages_conversation FOREIGN KEY (conversation_id) REFERENCES chat_conversations(id) ON DELETE CASCADE
);

-- Tạo index cho chat_messages
CREATE INDEX idx_chat_messages_conversation_created ON chat_messages(conversation_id, created_at DESC);
CREATE INDEX idx_chat_messages_type_status ON chat_messages(message_type, status);
CREATE INDEX idx_chat_messages_facebook_id ON chat_messages(facebook_message_id);
CREATE INDEX idx_chat_messages_ai_generated ON chat_messages(is_ai_generated);
CREATE INDEX idx_chat_messages_intent ON chat_messages(detected_intent);
CREATE INDEX idx_chat_messages_tenant ON chat_messages(tenant_id);

-- Tạo bảng facebook_page_configs (để lưu cấu hình Facebook Page)
DROP TABLE IF EXISTS facebook_page_configs CASCADE;
CREATE TABLE facebook_page_configs (
    id SERIAL PRIMARY KEY,
    page_id VARCHAR(100) NOT NULL,
    page_name VARCHAR(255) NOT NULL,
    access_token TEXT NOT NULL,
    app_id VARCHAR(100) NOT NULL,
    app_secret VARCHAR(255) NOT NULL,
    webhook_verify_token VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    webhook_url VARCHAR(500),
    subscribed_fields TEXT[], -- array of subscribed webhook fields
    permissions TEXT[], -- array of page permissions
    expires_at BIGINT, -- token expiration time
    metadata JSONB,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by INTEGER,
    updated_by INTEGER,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT facebook_page_configs_page_tenant_unique UNIQUE (page_id, tenant_id)
);

-- Tạo index cho facebook_page_configs
CREATE INDEX idx_facebook_page_configs_page_id ON facebook_page_configs(page_id);
CREATE INDEX idx_facebook_page_configs_tenant ON facebook_page_configs(tenant_id);
CREATE INDEX idx_facebook_page_configs_active ON facebook_page_configs(is_active);

-- Tạo bảng chat_sessions (để track session của user)
DROP TABLE IF EXISTS chat_sessions CASCADE;
CREATE TABLE chat_sessions (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    session_id VARCHAR(100) NOT NULL,
    user_context JSONB,
    current_intent VARCHAR(100),
    session_data JSONB,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    started_at BIGINT NOT NULL,
    ended_at BIGINT,
    last_activity_at BIGINT,
    tenant_id INTEGER NOT NULL,
    
    CONSTRAINT fk_chat_sessions_conversation FOREIGN KEY (conversation_id) REFERENCES chat_conversations(id) ON DELETE CASCADE,
    CONSTRAINT chat_sessions_session_id_unique UNIQUE (session_id)
);

-- Tạo index cho chat_sessions
CREATE INDEX idx_chat_sessions_conversation ON chat_sessions(conversation_id);
CREATE INDEX idx_chat_sessions_session_id ON chat_sessions(session_id);
CREATE INDEX idx_chat_sessions_active ON chat_sessions(is_active);
CREATE INDEX idx_chat_sessions_last_activity ON chat_sessions(last_activity_at DESC);

-- Tạo bảng ai_knowledge_base (để lưu dữ liệu cho RAG)
DROP TABLE IF EXISTS ai_knowledge_base CASCADE;
CREATE TABLE ai_knowledge_base (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(50) NOT NULL, -- 'faq', 'procedure', 'policy', 'data'
    source_type VARCHAR(50) NOT NULL, -- 'manual', 'auto_extracted', 'api_sync'
    source_reference VARCHAR(500), -- reference to original source
    embedding VECTOR(1536), -- OpenAI embedding vector (requires pgvector extension)
    metadata JSONB,
    tags TEXT[],
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by INTEGER,
    updated_by INTEGER,
    tenant_id INTEGER NOT NULL
);

-- Tạo index cho ai_knowledge_base
CREATE INDEX idx_ai_knowledge_base_content_type ON ai_knowledge_base(content_type);
CREATE INDEX idx_ai_knowledge_base_source_type ON ai_knowledge_base(source_type);
CREATE INDEX idx_ai_knowledge_base_tenant ON ai_knowledge_base(tenant_id);
CREATE INDEX idx_ai_knowledge_base_active ON ai_knowledge_base(is_active);
CREATE INDEX idx_ai_knowledge_base_tags ON ai_knowledge_base USING GIN(tags);

-- Tạo index cho vector similarity search (nếu có pgvector extension)
-- CREATE INDEX idx_ai_knowledge_base_embedding ON ai_knowledge_base USING ivfflat (embedding vector_cosine_ops);

-- Thêm comments cho các bảng
COMMENT ON TABLE chat_conversations IS 'Bảng lưu trữ các cuộc hội thoại chat';
COMMENT ON TABLE chat_messages IS 'Bảng lưu trữ tin nhắn trong cuộc hội thoại';
COMMENT ON TABLE facebook_page_configs IS 'Bảng cấu hình Facebook Page cho từng tenant';
COMMENT ON TABLE chat_sessions IS 'Bảng theo dõi session của người dùng trong chat';
COMMENT ON TABLE ai_knowledge_base IS 'Bảng lưu trữ dữ liệu kiến thức cho AI RAG system';

-- Thêm comments cho các cột quan trọng
COMMENT ON COLUMN chat_conversations.conversation_type IS 'Loại cuộc hội thoại: auto (AI), manual (human), mixed';
COMMENT ON COLUMN chat_messages.direction IS 'Hướng tin nhắn: incoming (từ user), outgoing (từ bot/agent)';
COMMENT ON COLUMN chat_messages.sender_type IS 'Người gửi: user, bot, agent';
COMMENT ON COLUMN ai_knowledge_base.embedding IS 'Vector embedding của nội dung (OpenAI ada-002, 1536 dimensions)';
